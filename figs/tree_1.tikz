% Arbre de décision
\begin{tikzpicture}[sibling distance=5em, scale=0.8,
    level distance=3em,
  every node/.style = {shape=rectangle, rounded corners,
    draw, align=center, top color=white},
  blue node/.style = {bottom color=blue!70, shape=circle},
  red node/.style = {bottom color=red!70, shape=circle}]
  
  \node {\(x_1 \leq 0.6\)}
    child { node[xshift=-1em] {\(x_2 \leq 0.7\)} 
        child {node[blue node] {B}}
        child {node[red node, xshift=-0.3em] {R}}
    }
    child { node[xshift=1em] {\(x_2 \leq 0.7\)} 
        child {node[red node] {R}}
        child {node[blue node, xshift=0.3em] {B}}
    };
\end{tikzpicture}
