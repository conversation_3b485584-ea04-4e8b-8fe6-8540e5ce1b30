This is pdfTeX, Version 3.141592653-2.6-1.40.22 (TeX Live 2021) (preloaded format=pdflatex 2022.1.21)  2 SEP 2025 16:22
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**"/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.tex"
(/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.tex
LaTeX2e <2020-10-01> patch level 4
L3 programming layer <2021-02-18> (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamer.cls
Document Class: beamer 2021/03/19 v3.62 A class for typesetting presentations
(/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasemodes.sty (/usr/local/texlive/2021/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count179
)
\beamer@tempbox=\box47
\beamer@tempcount=\count180
\c@beamerpauses=\count181
 (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasedecode.sty
\beamer@slideinframe=\count182
\beamer@minimum=\count183
\beamer@decode@box=\box48
)
\beamer@commentbox=\box49
\beamer@modecount=\count184
) (/usr/local/texlive/2021/texmf-dist/tex/generic/iftex/ifpdf.sty
Package: ifpdf 2019/10/25 v3.4 ifpdf legacy package. Use iftex instead.
 (/usr/local/texlive/2021/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2020/03/06 v1.0d TeX engine tests
))
\headdp=\dimen138
\footheight=\dimen139
\sidebarheight=\dimen140
\beamer@tempdim=\dimen141
\beamer@finalheight=\dimen142
\beamer@animht=\dimen143
\beamer@animdp=\dimen144
\beamer@animwd=\dimen145
\beamer@leftmargin=\dimen146
\beamer@rightmargin=\dimen147
\beamer@leftsidebar=\dimen148
\beamer@rightsidebar=\dimen149
\beamer@boxsize=\dimen150
\beamer@vboxoffset=\dimen151
\beamer@descdefault=\dimen152
\beamer@descriptionwidth=\dimen153
\beamer@lastskip=\skip47
\beamer@areabox=\box50
\beamer@animcurrent=\box51
\beamer@animshowbox=\box52
\beamer@sectionbox=\box53
\beamer@logobox=\box54
\beamer@linebox=\box55
\beamer@sectioncount=\count185
\beamer@subsubsectionmax=\count186
\beamer@subsectionmax=\count187
\beamer@sectionmax=\count188
\beamer@totalheads=\count189
\beamer@headcounter=\count190
\beamer@partstartpage=\count191
\beamer@sectionstartpage=\count192
\beamer@subsectionstartpage=\count193
\beamer@animationtempa=\count194
\beamer@animationtempb=\count195
\beamer@xpos=\count196
\beamer@ypos=\count197
\beamer@ypos@offset=\count198
\beamer@showpartnumber=\count199
\beamer@currentsubsection=\count266
\beamer@coveringdepth=\count267
\beamer@sectionadjust=\count268
\beamer@tocsectionnumber=\count269
 (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbaseoptions.sty (/usr/local/texlive/2021/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks15
))
\beamer@paperwidth=\skip48
\beamer@paperheight=\skip49
 (/usr/local/texlive/2021/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/local/texlive/2021/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count270
\Gm@cntv=\count271
\c@Gm@tempcnt=\count272
\Gm@bindingoffset=\dimen154
\Gm@wd@mp=\dimen155
\Gm@odd@mp=\dimen156
\Gm@even@mp=\dimen157
\Gm@layoutwidth=\dimen158
\Gm@layoutheight=\dimen159
\Gm@layouthoffset=\dimen160
\Gm@layoutvoffset=\dimen161
\Gm@dimlist=\toks16
) (/usr/local/texlive/2021/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2020/04/10 v1.4m Standard LaTeX file (size option)
) (/usr/local/texlive/2021/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/usr/local/texlive/2021/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2020/09/09 v1.2b Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2021/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2020/08/30 v1.4c Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2021/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2016/01/03 v1.10 sin cos tan (DPC)
) (/usr/local/texlive/2021/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 105.
 (/usr/local/texlive/2021/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2020/10/05 v1.2a Graphics/color driver for pdftex
))
\Gin@req@height=\dimen162
\Gin@req@width=\dimen163
) (/usr/local/texlive/2021/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/usr/local/texlive/2021/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks17
\pgfutil@tempdima=\dimen164
\pgfutil@tempdimb=\dimen165
 (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/utilities/pgfutil-common-lists.tex)) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box56
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2020/12/27 v3.1.8b (3.1.8b)
)) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2020/12/27 v3.1.8b (3.1.8b)
 (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks18
\pgfkeys@temptoks=\toks19
 (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/utilities/pgfkeysfiltered.code.tex
\pgfkeys@tmptoks=\toks20
))
\pgf@x=\dimen166
\pgf@y=\dimen167
\pgf@xa=\dimen168
\pgf@ya=\dimen169
\pgf@xb=\dimen170
\pgf@yb=\dimen171
\pgf@xc=\dimen172
\pgf@yc=\dimen173
\pgf@xd=\dimen174
\pgf@yd=\dimen175
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count273
\c@pgf@countb=\count274
\c@pgf@countc=\count275
\c@pgf@countd=\count276
\t@pgf@toka=\toks21
\t@pgf@tokb=\toks22
\t@pgf@tokc=\toks23
\pgf@sys@id@count=\count277
 (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2020/12/27 v3.1.8b (3.1.8b)
)
Driver file for pgf: pgfsys-pdftex.def
 (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2020/12/27 v3.1.8b (3.1.8b)
 (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2020/12/27 v3.1.8b (3.1.8b)
))) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgfsyssoftpath@smallbuffer@items=\count278
\pgfsyssoftpath@bigbuffer@items=\count279
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2020/12/27 v3.1.8b (3.1.8b)
)) (/usr/local/texlive/2021/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2016/05/11 v2.12 LaTeX color extensions (UK)
 (/usr/local/texlive/2021/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 225.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1348.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1352.
Package xcolor Info: Model `RGB' extended on input line 1364.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1366.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1367.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1370.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1371.
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2020/12/27 v3.1.8b (3.1.8b)
 (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen176
\pgfmath@count=\count280
\pgfmath@box=\box57
\pgfmath@toks=\toks24
\pgfmath@stack@operand=\toks25
\pgfmath@stack@operation=\toks26
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex))) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count281
)) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgf@picminx=\dimen177
\pgf@picmaxx=\dimen178
\pgf@picminy=\dimen179
\pgf@picmaxy=\dimen180
\pgf@pathminx=\dimen181
\pgf@pathmaxx=\dimen182
\pgf@pathminy=\dimen183
\pgf@pathmaxy=\dimen184
\pgf@xx=\dimen185
\pgf@xy=\dimen186
\pgf@yx=\dimen187
\pgf@yy=\dimen188
\pgf@zx=\dimen189
\pgf@zy=\dimen190
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgf@path@lastx=\dimen191
\pgf@path@lasty=\dimen192
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgf@shorten@end@additional=\dimen193
\pgf@shorten@start@additional=\dimen194
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgfpic=\box58
\pgf@hbox=\box59
\pgf@layerbox@main=\box60
\pgf@picture@serial@count=\count282
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgflinewidth=\dimen195
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgf@pt@x=\dimen196
\pgf@pt@y=\dimen197
\pgf@pt@temp=\dimen198
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2020/12/27 v3.1.8b (3.1.8b)
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2020/12/27 v3.1.8b (3.1.8b)
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2020/12/27 v3.1.8b (3.1.8b)
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgfarrowsep=\dimen199
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgf@max=\dimen256
\pgf@sys@shading@range@num=\count283
\pgf@shadingcount=\count284
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2020/12/27 v3.1.8b (3.1.8b)
 (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgfexternal@startupbox=\box61
)) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2020/12/27 v3.1.8b (3.1.8b)
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2020/12/27 v3.1.8b (3.1.8b)
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2020/12/27 v3.1.8b (3.1.8b)
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2020/12/27 v3.1.8b (3.1.8b)
))) (/usr/local/texlive/2021/texmf-dist/tex/latex/pgf/utilities/xxcolor.sty
Package: xxcolor 2003/10/24 ver 0.1
\XC@nummixins=\count285
\XC@countmixins=\count286
) (/usr/local/texlive/2021/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2020/08/17 v1.0a Emulation of the original atbegshi package
with kernel methods
) (/usr/local/texlive/2021/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2021-02-27 v7.00k Hypertext links for LaTeX
 (/usr/local/texlive/2021/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2020-05-10 v1.25 LaTeX kernel commands for general use (HO)
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/local/texlive/2021/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
) (/usr/local/texlive/2021/texmf-dist/tex/generic/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2019/12/15 v1.18 Key value parser (HO)
) (/usr/local/texlive/2021/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
) (/usr/local/texlive/2021/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/usr/local/texlive/2021/texmf-dist/tex/latex/letltxmacro/letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
) (/usr/local/texlive/2021/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (/usr/local/texlive/2021/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2020-10-07 v3.14 Key value format for package options (HO)
)
\@linkdim=\dimen257
\Hy@linkcounter=\count287
\Hy@pagecounter=\count288
 (/usr/local/texlive/2021/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2021-02-27 v7.00k Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (/usr/local/texlive/2021/texmf-dist/tex/latex/hyperref/hyperref-langpatches.def
File: hyperref-langpatches.def 2021-02-27 v7.00k Hyperref: patches for babel languages
) (/usr/local/texlive/2021/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
) (/usr/local/texlive/2021/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
)
\Hy@SavedSpaceFactor=\count289
 (/usr/local/texlive/2021/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2021-02-27 v7.00k Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `bookmarks' set `true' on input line 4073.
Package hyperref Info: Option `bookmarksopen' set `true' on input line 4073.
Package hyperref Info: Option `implicit' set `false' on input line 4073.
Package hyperref Info: Hyper figures OFF on input line 4192.
Package hyperref Info: Link nesting OFF on input line 4197.
Package hyperref Info: Hyper index ON on input line 4200.
Package hyperref Info: Plain pages OFF on input line 4207.
Package hyperref Info: Backreferencing OFF on input line 4212.
Package hyperref Info: Implicit mode OFF; no redefinition of LaTeX internals.
Package hyperref Info: Bookmarks ON on input line 4445.
\c@Hy@tempcnt=\count290
 (/usr/local/texlive/2021/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip16
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4804.
\XeTeXLinkMargin=\dimen258
 (/usr/local/texlive/2021/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (/usr/local/texlive/2021/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count291
\Field@Width=\dimen259
\Fld@charsize=\dimen260
Package hyperref Info: Hyper figures OFF on input line 6075.
Package hyperref Info: Link nesting OFF on input line 6080.
Package hyperref Info: Hyper index ON on input line 6083.
Package hyperref Info: backreferencing OFF on input line 6090.
Package hyperref Info: Link coloring OFF on input line 6095.
Package hyperref Info: Link coloring with OCG OFF on input line 6100.
Package hyperref Info: PDF/A mode OFF on input line 6105.
LaTeX Info: Redefining \ref on input line 6145.
LaTeX Info: Redefining \pageref on input line 6149.
\Hy@abspage=\count292


Package hyperref Message: Stopped early.

)
Package hyperref Info: Driver (autodetected): hpdftex.
 (/usr/local/texlive/2021/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2021-02-27 v7.00k Hyperref driver for pdfTeX
 (/usr/local/texlive/2021/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atvery package
with kernel methods
)
\Fld@listcount=\count293
\c@bookmark@seq@number=\count294
 (/usr/local/texlive/2021/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2019/12/05 v1.9 Rerun checks for auxiliary files (HO)
 (/usr/local/texlive/2021/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 286.
)) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbaserequires.sty (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasecompatibility.sty) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasefont.sty (/usr/local/texlive/2021/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/local/texlive/2021/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks27
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/local/texlive/2021/texmf-dist/tex/latex/sansmathaccent/sansmathaccent.sty
Package: sansmathaccent 2020/01/31
 (/usr/local/texlive/2021/texmf-dist/tex/latex/koma-script/scrlfile.sty
Package: scrlfile 2021/03/17 v3.33 KOMA-Script package (file load hooks)
 (/usr/local/texlive/2021/texmf-dist/tex/latex/koma-script/scrlfile-hook.sty
Package: scrlfile-hook 2021/03/17 v3.33 KOMA-Script package (using LaTeX hooks)

LaTeX3 Info: Defining command \BeforeFile with sig. 'm' on line 61.


LaTeX3 Info: Defining command \AfterFile with sig. 'm' on line 65.


LaTeX3 Info: Defining command \BeforeClass with sig. 'm' on line 69.


LaTeX3 Info: Defining command \BeforePackage with sig. 'm' on line 73.


LaTeX3 Info: Defining command \AfterAtEndOfClass with sig. 'smo+m' on line 83.


LaTeX3 Info: Defining command \AfterAtEndOfPackage with sig. 'smo+m' on line
(LaTeX3)     93.


LaTeX3 Info: Defining command \scrlfile@AfterClass with sig. 'smo+m' on line
(LaTeX3)     173.


LaTeX3 Info: Defining command \AfterClass with sig. '' on line 174.


LaTeX3 Info: Defining command \scrlfile@AfterPackage with sig. 'smo+m' on line
(LaTeX3)     191.


LaTeX3 Info: Defining command \AfterPackage with sig. '' on line 192.


LaTeX3 Info: Defining command \ReplaceInput with sig. '' on line 193.


LaTeX3 Info: Defining command \ReplaceClass with sig. 'mm' on line 196.


LaTeX3 Info: Defining command \ReplacePackage with sig. 'mm' on line 199.


LaTeX3 Info: Defining command \UnReplaceInput with sig. '' on line 200.


LaTeX3 Info: Defining command \UnReplaceClass with sig. 'm' on line 203.


LaTeX3 Info: Defining command \UnReplacePackage with sig. 'mm' on line 206.


LaTeX3 Info: Defining command \PreventPackageFromLoading with sig. 's+om' on
(LaTeX3)     line 234.


LaTeX3 Info: Defining command \StorePreventPackageFromLoading with sig. 'm' on
(LaTeX3)     line 242.


LaTeX3 Info: Defining command \ResetPreventPackageFromLoading with sig. '' on
(LaTeX3)     line 247.


LaTeX3 Info: Defining command \UnPreventPackageFromLoading with sig. 'sm' on
(LaTeX3)     line 261.


LaTeX3 Info: Defining command \BeforeClosingMainAux with sig. 'om' on line
(LaTeX3)     274.


LaTeX3 Info: Defining command \AfterReadingMainAux with sig. 'om' on line 287.


LaTeX3 Info: Defining command \protected@immediate@write with sig. 'm+m+m' on
(LaTeX3)     line 298.

 (/usr/local/texlive/2021/texmf-dist/tex/latex/koma-script/scrlogo.sty
Package: scrlogo 2021/03/17 v3.33 KOMA-Script package (logo)
))))) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasetranslator.sty (/usr/local/texlive/2021/texmf-dist/tex/latex/translator/translator.sty
Package: translator 2020-08-03 v1.12c Easy translation of strings in LaTeX
)) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasemisc.sty) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasetwoscreens.sty) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbaseoverlay.sty
\beamer@argscount=\count295
\beamer@lastskipcover=\skip50
\beamer@trivlistdepth=\count296
) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasetitle.sty) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasesection.sty
\c@lecture=\count297
\c@part=\count298
\c@section=\count299
\c@subsection=\count300
\c@subsubsection=\count301
) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbaseframe.sty
\beamer@framebox=\box62
\beamer@frametitlebox=\box63
\beamer@zoombox=\box64
\beamer@zoomcount=\count302
\beamer@zoomframecount=\count303
\beamer@frametextheight=\dimen261
\c@subsectionslide=\count304
\beamer@frametopskip=\skip51
\beamer@framebottomskip=\skip52
\beamer@frametopskipautobreak=\skip53
\beamer@framebottomskipautobreak=\skip54
\beamer@envbody=\toks28
\framewidth=\dimen262
\c@framenumber=\count305
) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbaseverbatim.sty
\beamer@verbatimfileout=\write4
) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbaseframesize.sty
\beamer@splitbox=\box65
\beamer@autobreakcount=\count306
\beamer@autobreaklastheight=\dimen263
\beamer@frametitletoks=\toks29
\beamer@framesubtitletoks=\toks30
) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbaseframecomponents.sty
\beamer@footins=\box66
) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasecolor.sty) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasenotes.sty
\beamer@frameboxcopy=\box67
) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasetoc.sty) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasetemplates.sty
\beamer@sbttoks=\toks31
 (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbaseauxtemplates.sty (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbaseboxes.sty
\bmb@box=\box68
\bmb@colorbox=\box69
\bmb@boxwidth=\dimen264
\bmb@boxheight=\dimen265
\bmb@prevheight=\dimen266
\bmb@temp=\dimen267
\bmb@dima=\dimen268
\bmb@dimb=\dimen269
\bmb@prevheight=\dimen270
)
\beamer@blockheadheight=\dimen271
)) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbaselocalstructure.sty (/usr/local/texlive/2021/texmf-dist/tex/latex/tools/enumerate.sty
Package: enumerate 2015/07/23 v3.00 enumerate extensions (DPC)
\@enLab=\toks32
)
\beamer@bibiconwidth=\skip55
\c@figure=\count307
\c@table=\count308
\abovecaptionskip=\skip56
\belowcaptionskip=\skip57
) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasenavigation.sty
\beamer@section@min@dim=\dimen272
) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasetheorems.sty (/usr/local/texlive/2021/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2020/09/23 v2.17i AMS math features
\@mathmargin=\skip58

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2021/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2000/06/29 v2.01 AMS text
 (/usr/local/texlive/2021/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks33
\ex@=\dimen273
)) (/usr/local/texlive/2021/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen274
) (/usr/local/texlive/2021/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2016/03/08 v2.02 operator names
)
\inf@bad=\count309
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count310
\leftroot@=\count311
LaTeX Info: Redefining \overline on input line 399.
\classnum@=\count312
\DOTSCASE@=\count313
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box70
\strutbox@=\box71
\big@size=\dimen275
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count314
\c@MaxMatrixCols=\count315
\dotsspace@=\muskip17
\c@parentequation=\count316
\dspbrk@lvl=\count317
\tag@help=\toks34
\row@=\count318
\column@=\count319
\maxfields@=\count320
\andhelp@=\toks35
\eqnshift@=\dimen276
\alignsep@=\dimen277
\tagshift@=\dimen278
\tagwidth@=\dimen279
\totwidth@=\dimen280
\lineht@=\dimen281
\@envbody=\toks36
\multlinegap=\skip59
\multlinetaggap=\skip60
\mathdisplay@stack=\toks37
LaTeX Info: Redefining \[ on input line 2923.
LaTeX Info: Redefining \] on input line 2924.
) (/usr/local/texlive/2021/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks38
\thm@bodyfont=\toks39
\thm@headfont=\toks40
\thm@notefont=\toks41
\thm@headpunct=\toks42
\thm@preskip=\skip61
\thm@postskip=\skip62
\thm@headsep=\skip63
\dth@everypar=\toks43
)
\c@theorem=\count321
) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerbasethemes.sty)) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerthemedefault.sty (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerfontthemedefault.sty) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamercolorthemedefault.sty) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerinnerthemedefault.sty
\beamer@dima=\dimen282
\beamer@dimb=\dimen283
) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerouterthemedefault.sty))) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerthemeBoadilla.sty (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamercolorthemerose.sty) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerinnerthemerounded.sty) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamercolorthemedolphin.sty) (/usr/local/texlive/2021/texmf-dist/tex/latex/beamer/beamerouterthemeinfolines.sty)) (/usr/local/texlive/2021/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/usr/local/texlive/2021/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
Package: pgf 2020/12/27 v3.1.8b (3.1.8b)
 (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgfnodeparttextbox=\box72
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2020/12/27 v3.1.8b (3.1.8b)
) (/usr/local/texlive/2021/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2020/12/27 v3.1.8b (3.1.8b)
\pgf@nodesepstart=\dimen284
\pgf@nodesepend=\dimen285
) (/usr/local/texlive/2021/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2020/12/27 v3.1.8b (3.1.8b)
)) (/usr/local/texlive/2021/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/usr/local/texlive/2021/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/usr/local/texlive/2021/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2020/12/27 v3.1.8b (3.1.8b)
 (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)
\pgffor@iter=\dimen286
\pgffor@skip=\dimen287
\pgffor@stack=\toks44
\pgffor@toks=\toks45
)) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2020/12/27 v3.1.8b (3.1.8b)
 (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgf@plot@mark@count=\count322
\pgfplotmarksize=\dimen288
)
\tikz@lastx=\dimen289
\tikz@lasty=\dimen290
\tikz@lastxsaved=\dimen291
\tikz@lastysaved=\dimen292
\tikz@lastmovetox=\dimen293
\tikz@lastmovetoy=\dimen294
\tikzleveldistance=\dimen295
\tikzsiblingdistance=\dimen296
\tikz@figbox=\box73
\tikz@figbox@bg=\box74
\tikz@tempbox=\box75
\tikz@tempbox@bg=\box76
\tikztreelevel=\count323
\tikznumberofchildren=\count324
\tikznumberofcurrentchild=\count325
\tikz@fig@count=\count326
 (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgfmatrixcurrentrow=\count327
\pgfmatrixcurrentcolumn=\count328
\pgf@matrix@numberofcolumns=\count329
)
\tikz@expandcount=\count330
 (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2020/12/27 v3.1.8b (3.1.8b)
))) (/usr/local/texlive/2021/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2020/10/26 v3.5g Customizing captions (AR)
 (/usr/local/texlive/2021/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2020/10/21 v2.2e caption3 kernel (AR)
\captionmargin=\dimen297
\captionmargin@=\dimen298
\captionwidth=\dimen299
\caption@tempdima=\dimen300
\caption@indent=\dimen301
\caption@parindent=\dimen302
\caption@hangindent=\dimen303
Package caption Info: beamer document class detected.
 (/usr/local/texlive/2021/texmf-dist/tex/latex/caption/caption-beamer.sto
File: caption-beamer.sto 2020/08/22 v2.0 Adaption of the caption package to the beamer document classes (AR)
))
\c@caption@flags=\count331
\c@continuedfloat=\count332
Package caption Info: hyperref package is loaded.
Package caption Info: Hyperref support is turned off
(caption)             because hyperref has stopped early.
) (/usr/local/texlive/2021/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarybackgrounds.code.tex
File: tikzlibrarybackgrounds.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgf@layerbox@background=\box77
\pgf@layerboxsaved@background=\box78
) (/usr/local/texlive/2021/texmf-dist/tex/latex/algorithm2e/algorithm2e.sty
Package: algorithm2e 2017/07/18 v5.2 algorithms environments
\c@AlgoLine=\count333
 (/usr/local/texlive/2021/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2014/09/29 v1.1c Standard LaTeX ifthen package (DPC)
)
\algocf@hangindent=\skip64
 (/usr/local/texlive/2021/texmf-dist/tex/latex/ifoddpage/ifoddpage.sty
Package: ifoddpage 2016/04/23 v1.1 Conditionals for odd/even page detection
\c@checkoddpage=\count334
) (/usr/local/texlive/2021/texmf-dist/tex/latex/tools/xspace.sty
Package: xspace 2014/10/28 v1.13 Space after command names (DPC,MH)
) (/usr/local/texlive/2021/texmf-dist/tex/latex/relsize/relsize.sty
Package: relsize 2013/03/29 ver 4.1
)
\skiptotal=\skip65
\skiplinenumber=\skip66
\skiprule=\skip67
\skiphlne=\skip68
\skiptext=\skip69
\skiplength=\skip70
\algomargin=\skip71
\skipalgocfslide=\skip72
\algowidth=\dimen304
\inoutsize=\dimen305
\inoutindent=\dimen306
\interspacetitleruled=\dimen307
\interspacealgoruled=\dimen308
\interspacetitleboxruled=\dimen309
\algocf@ruledwidth=\skip73
\algocf@inoutbox=\box79
\algocf@inputbox=\box80
\AlCapSkip=\skip74
\AlCapHSkip=\skip75
\algoskipindent=\skip76
\algocf@nlbox=\box81
\algocf@hangingbox=\box82
\algocf@untilbox=\box83
\algocf@skipuntil=\skip77
\algocf@capbox=\box84
\algocf@lcaptionbox=\skip78
\algoheightruledefault=\skip79
\algoheightrule=\skip80
\algotitleheightruledefault=\skip81
\algotitleheightrule=\skip82
\c@algocfline=\count335
\c@algocfproc=\count336
\c@algocf=\count337
\algocf@algoframe=\box85
\algocf@algobox=\box86
) (/usr/local/texlive/2021/texmf-dist/tex/latex/makecell/makecell.sty
Package: makecell 2009/08/03 V0.1e Managing of Tab Column Heads and Cells
 (/usr/local/texlive/2021/texmf-dist/tex/latex/tools/array.sty
Package: array 2020/10/01 v2.5c Tabular extension package (FMi)
\col@sep=\dimen310
\ar@mcellbox=\box87
\extrarowheight=\dimen311
\NC@list=\toks46
\extratabsurround=\skip83
\backup@length=\skip84
\ar@cellbox=\box88
)
\rotheadsize=\dimen312
\c@nlinenum=\count338
\TeXr@lab=\toks47
) (/usr/local/texlive/2021/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2020/10/07 v1.3j Sub-captions (AR)
\c@subfigure=\count339
\c@subtable=\count340
) (/usr/local/texlive/2021/texmf-dist/tex/latex/doublestroke/dsfont.sty
Package: dsfont 1995/08/01 v0.1 Double stroke roman fonts
) (/usr/local/texlive/2021/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2021-03-18 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count341
\l__pdf_internal_box=\box89
) (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.aux

LaTeX Warning: Label `fig:sfig1' multiply defined.


LaTeX Warning: Label `sub@fig:sfig1' multiply defined.


LaTeX Warning: Label `fig:sfig2' multiply defined.


LaTeX Warning: Label `sub@fig:sfig2' multiply defined.

)
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 43.
LaTeX Font Info:    ... okay on input line 43.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: custom
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: includehead includefoot 
* h-part:(L,W,R)=(10.95003pt, 433.34402pt, 10.95003pt)
* v-part:(T,H,B)=(0.0pt, 256.0748pt, 0.0pt)
* \paperwidth=455.24408pt
* \paperheight=256.0748pt
* \textwidth=433.34402pt
* \textheight=227.62207pt
* \oddsidemargin=-61.31996pt
* \evensidemargin=-61.31996pt
* \topmargin=-72.26999pt
* \headheight=14.22636pt
* \headsep=0.0pt
* \topskip=11.0pt
* \footskip=14.22636pt
* \marginparwidth=4.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/local/texlive/2021/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count342
\scratchdimen=\dimen313
\scratchbox=\box90
\nofMPsegments=\count343
\nofMParguments=\count344
\everyMPshowfont=\toks48
\MPscratchCnt=\count345
\MPscratchDim=\dimen314
\MPnumerator=\count346
\makeMPintoPDFobject=\count347
\everyMPtoPDFconversion=\toks49
) (/usr/local/texlive/2021/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/local/texlive/2021/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
Package hyperref Info: Link coloring OFF on input line 43.
 (/usr/local/texlive/2021/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2021-04-02 v2.47 Cross-referencing by name of section
 (/usr/local/texlive/2021/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/usr/local/texlive/2021/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count348
)
LaTeX Info: Redefining \ref on input line 43.
LaTeX Info: Redefining \pageref on input line 43.
LaTeX Info: Redefining \nameref on input line 43.
 (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.out) (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.out)
\@outlinefile=\write5
\openout5 = `main.out'.

LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/cmss/m/n on input line 43.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/cmss/b/n on input line 43.
\symnumbers=\mathgroup6
\sympureletters=\mathgroup7
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/cmr/m/n on input line 43.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 43.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/cmss/b/n on input line 43.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/cmss/b/n on input line 43.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 43.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/cmss/m/n on input line 43.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/cmss/m/n on input line 43.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 43.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/cmss/m/it on input line 43.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/cmss/m/it on input line 43.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 43.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/cmtt/m/n on input line 43.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/cmtt/m/n on input line 43.
LaTeX Font Info:    Overwriting symbol font `numbers' in version `bold'
(Font)                  OT1/cmss/m/n --> OT1/cmss/b/n on input line 43.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `bold'
(Font)                  OT1/cmss/m/it --> OT1/cmss/b/it on input line 43.
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `bold'
(Font)                  OT1/cmss/b/n --> OT1/cmr/b/n on input line 43.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmss/b/n --> OT1/cmss/b/n on input line 43.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/m/n --> OT1/cmss/b/n on input line 43.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmss/m/it --> OT1/cmss/b/it on input line 43.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/cmtt/b/n on input line 43.
LaTeX Font Info:    Redeclaring symbol font `pureletters' on input line 43.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `normal'
(Font)                  OT1/cmss/m/it --> OT1/mathkerncmss/m/sl on input line 43.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `bold'
(Font)                  OT1/cmss/b/it --> OT1/mathkerncmss/m/sl on input line 43.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `bold'
(Font)                  OT1/mathkerncmss/m/sl --> OT1/mathkerncmss/bx/sl on input line 43.
 (/usr/local/texlive/2021/texmf-dist/tex/latex/translator/translator-basic-dictionary-English.dict
Dictionary: translator-basic-dictionary, Language: English 
) (/usr/local/texlive/2021/texmf-dist/tex/latex/translator/translator-bibliography-dictionary-English.dict
Dictionary: translator-bibliography-dictionary, Language: English 
) (/usr/local/texlive/2021/texmf-dist/tex/latex/translator/translator-environment-dictionary-English.dict
Dictionary: translator-environment-dictionary, Language: English 
) (/usr/local/texlive/2021/texmf-dist/tex/latex/translator/translator-months-dictionary-English.dict
Dictionary: translator-months-dictionary, Language: English 
) (/usr/local/texlive/2021/texmf-dist/tex/latex/translator/translator-numbers-dictionary-English.dict
Dictionary: translator-numbers-dictionary, Language: English 
) (/usr/local/texlive/2021/texmf-dist/tex/latex/translator/translator-theorem-dictionary-English.dict
Dictionary: translator-theorem-dictionary, Language: English 
)
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: End \AtBeginDocument code.
 (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.nav)
LaTeX Font Info:    Trying to load font information for U+msa on input line 45.
 (/usr/local/texlive/2021/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 45.
 (/usr/local/texlive/2021/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for OT1+mathkerncmss on input line 45.
 (/usr/local/texlive/2021/texmf-dist/tex/latex/sansmathaccent/ot1mathkerncmss.fd
File: ot1mathkerncmss.fd 2020/01/31 Fontinst v1.933 font definitions for OT1/mathkerncmss.
) [1

{/usr/local/texlive/2021/texmf-var/fonts/map/pdftex/updmap/pdftex.map}] (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.toc) [2

] (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.toc) [3

]
LaTeX Font Info:    Trying to load font information for TS1+cmss on input line 114.
 (/usr/local/texlive/2021/texmf-dist/tex/latex/base/ts1cmss.fd
File: ts1cmss.fd 2019/12/16 v2.5j Standard LaTeX font definitions
)

pdfTeX warning: pdflatex (file /Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/model.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<figs/model.pdf, id=197, 597.50786pt x 845.04686pt>
File: figs/model.pdf Graphic file (type pdf)
<use figs/model.pdf>
Package pdftex.def Info: figs/model.pdf  used on input line 114.
(pdftex.def)             Requested size: 86.66748pt x 122.57378pt.
<figs/Sample_User_Icon.png, id=198, 385.44pt x 385.44pt>
File: figs/Sample_User_Icon.png Graphic file (type png)
<use figs/Sample_User_Icon.png>
Package pdftex.def Info: figs/Sample_User_Icon.png  used on input line 114.
(pdftex.def)             Requested size: 56.33684pt x 56.3314pt.
[4

 </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/model.pdf> </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/Sample_User_Icon.png>]
File: figs/model.pdf Graphic file (type pdf)
<use figs/model.pdf>
Package pdftex.def Info: figs/model.pdf  used on input line 114.
(pdftex.def)             Requested size: 86.66748pt x 122.57378pt.
File: figs/Sample_User_Icon.png Graphic file (type png)
<use figs/Sample_User_Icon.png>
Package pdftex.def Info: figs/Sample_User_Icon.png  used on input line 114.
(pdftex.def)             Requested size: 56.33684pt x 56.3314pt.
LaTeX Font Info:    Font shape `OT1/cmss/b/n' in size <10.95> not available
(Font)              Font shape `OT1/cmss/bx/n' tried instead on input line 114.
LaTeX Font Info:    Font shape `OT1/cmss/b/n' in size <8> not available
(Font)              Font shape `OT1/cmss/bx/n' tried instead on input line 114.
LaTeX Font Info:    Font shape `OT1/cmss/b/n' in size <6> not available
(Font)              Font shape `OT1/cmss/bx/n' tried instead on input line 114.
 [5

]
File: figs/model.pdf Graphic file (type pdf)
<use figs/model.pdf>
Package pdftex.def Info: figs/model.pdf  used on input line 114.
(pdftex.def)             Requested size: 86.66748pt x 122.57378pt.
File: figs/Sample_User_Icon.png Graphic file (type png)
<use figs/Sample_User_Icon.png>
Package pdftex.def Info: figs/Sample_User_Icon.png  used on input line 114.
(pdftex.def)             Requested size: 56.33684pt x 56.3314pt.
 [6

]
File: figs/model.pdf Graphic file (type pdf)
<use figs/model.pdf>
Package pdftex.def Info: figs/model.pdf  used on input line 114.
(pdftex.def)             Requested size: 86.66748pt x 122.57378pt.
<figs/hacker-logo.jpg, id=288, 760.8425pt x 501.875pt>
File: figs/hacker-logo.jpg Graphic file (type jpg)
<use figs/hacker-logo.jpg>
Package pdftex.def Info: figs/hacker-logo.jpg  used on input line 114.
(pdftex.def)             Requested size: 60.6679pt x 40.01297pt.
File: figs/model.pdf Graphic file (type pdf)
<use figs/model.pdf>
Package pdftex.def Info: figs/model.pdf  used on input line 114.
(pdftex.def)             Requested size: 30.33725pt x 42.89954pt.
 [7

 </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/hacker-logo.jpg>] (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/tree_1.tikz)
Overfull \hbox (4.0304pt too wide) in paragraph at lines 18--136
 [][] 
 []

(/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/graph_1.tikz
Overfull \hbox (46.78737pt too wide) in paragraph at lines 25--26
 [][] 
 []

) [8

]
LaTeX Font Info:    Font shape `OT1/cmss/b/n' in size <9> not available
(Font)              Font shape `OT1/cmss/bx/n' tried instead on input line 149.
LaTeX Font Info:    Font shape `OT1/cmss/b/n' in size <5> not available
(Font)              Font shape `OT1/cmss/bx/n' tried instead on input line 149.
 [9

] (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/exampleCF.tex
LaTeX Font Info:    Font shape `OT1/cmss/b/n' in size <10> not available
(Font)              Font shape `OT1/cmss/bx/n' tried instead on input line 54.
LaTeX Font Info:    Font shape `OT1/cmss/b/n' in size <7> not available
(Font)              Font shape `OT1/cmss/bx/n' tried instead on input line 54.


LaTeX Warning: Reference `fig:DTexample' on page 10 undefined on input line 54.


LaTeX Warning: Reference `fig:DTexample' on page 10 undefined on input line 54.

)
Overfull \vbox (68.7903pt too high) detected at line 155
 []

[10

]
File: figs/model.pdf Graphic file (type pdf)
<use figs/model.pdf>
Package pdftex.def Info: figs/model.pdf  used on input line 203.
(pdftex.def)             Requested size: 86.66748pt x 122.57378pt.
File: figs/hacker-logo.jpg Graphic file (type jpg)
<use figs/hacker-logo.jpg>
Package pdftex.def Info: figs/hacker-logo.jpg  used on input line 203.
(pdftex.def)             Requested size: 64.99895pt x 42.8694pt.
File: figs/model.pdf Graphic file (type pdf)
<use figs/model.pdf>
Package pdftex.def Info: figs/model.pdf  used on input line 203.
(pdftex.def)             Requested size: 38.99937pt x 55.14917pt.
 [11

]
<figs/DTlogo.png, id=450, 977.6525pt x 574.145pt>
File: figs/DTlogo.png Graphic file (type png)
<use figs/DTlogo.png>
Package pdftex.def Info: figs/DTlogo.png  used on input line 203.
(pdftex.def)             Requested size: 112.66705pt x 66.16107pt.
File: figs/hacker-logo.jpg Graphic file (type jpg)
<use figs/hacker-logo.jpg>
Package pdftex.def Info: figs/hacker-logo.jpg  used on input line 203.
(pdftex.def)             Requested size: 64.99895pt x 42.8694pt.
File: figs/DTlogo.png Graphic file (type png)
<use figs/DTlogo.png>
Package pdftex.def Info: figs/DTlogo.png  used on input line 203.
(pdftex.def)             Requested size: 56.33684pt x 33.08054pt.
 [12

 </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/DTlogo.png>] (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.toc) [13

] [14

]
<figs/CF-DualCF.png, id=546, 488.82625pt x 424.58624pt>
File: figs/CF-DualCF.png Graphic file (type png)
<use figs/CF-DualCF.png>
Package pdftex.def Info: figs/CF-DualCF.png  used on input line 245.
(pdftex.def)             Requested size: 138.66849pt x 120.44469pt.
 [15

 </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/CF-DualCF.png>]
<figs/DualCF.png, id=574, 488.82625pt x 424.58624pt>
File: figs/DualCF.png Graphic file (type png)
<use figs/DualCF.png>
Package pdftex.def Info: figs/DualCF.png  used on input line 245.
(pdftex.def)             Requested size: 138.66849pt x 120.44469pt.
 [16

 </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/DualCF.png>]
<figs/PathFinding.png, id=601, 917.4275pt x 443.6575pt>
File: figs/PathFinding.png Graphic file (type png)
<use figs/PathFinding.png>
Package pdftex.def Info: figs/PathFinding.png  used on input line 267.
(pdftex.def)             Requested size: 138.66849pt x 67.05347pt.
 [17

 </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/PathFinding.png>] (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.toc) [18

]
\openout4 = `main.vrb'.

 (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.vrb
Missing character: There is no [ in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
)
Overfull \vbox (5.2576pt too high) detected at line 360
 []

[19

] (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.vrb
Missing character: There is no [ in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
)
Overfull \vbox (5.2576pt too high) detected at line 360
 []

[20

] (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.vrb
Missing character: There is no [ in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
)
Overfull \vbox (5.2576pt too high) detected at line 360
 []

[21

] (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.vrb
Missing character: There is no [ in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
)
Overfull \vbox (5.2576pt too high) detected at line 360
 []

[22

] (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.vrb
Missing character: There is no [ in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
)
Overfull \vbox (5.2576pt too high) detected at line 360
 []

[23

] (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.vrb
Missing character: There is no [ in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no d in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
Missing character: There is no [ in font nullfont!
Missing character: There is no b in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no u in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ! in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no ] in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 6 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 0 in font nullfont!
Missing character: There is no . in font nullfont!
Missing character: There is no 7 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no r in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no c in font nullfont!
Missing character: There is no t in font nullfont!
Missing character: There is no a in font nullfont!
Missing character: There is no n in font nullfont!
Missing character: There is no g in font nullfont!
Missing character: There is no l in font nullfont!
Missing character: There is no e in font nullfont!
Missing character: There is no ( in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no , in font nullfont!
Missing character: There is no 1 in font nullfont!
Missing character: There is no ) in font nullfont!
Missing character: There is no ; in font nullfont!
)
Overfull \vbox (5.2576pt too high) detected at line 360
 []

[24

] (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.vrb)
Overfull \vbox (5.2576pt too high) detected at line 360
 []

[25

]
Overfull \hbox (25.11884pt too wide) in paragraph at lines 400--400
 [][] 
 []

[26

]
Overfull \hbox (25.11884pt too wide) in paragraph at lines 400--400
 [][] 
 []

[27

] (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.toc) [28

] [29

] [30

] [31

] [32

] [33

]
LaTeX Font Info:    Trying to load font information for U+dsrom on input line 457.
 (/usr/local/texlive/2021/texmf-dist/tex/latex/doublestroke/Udsrom.fd
File: Udsrom.fd 1995/08/01 v0.1 Double stroke roman font definitions
) [34

] [35

]
<figs/P4_COMPAS.pdf, id=1240, 722.7pt x 433.62pt>
File: figs/P4_COMPAS.pdf Graphic file (type pdf)
<use figs/P4_COMPAS.pdf>
Package pdftex.def Info: figs/P4_COMPAS.pdf  used on input line 497.
(pdftex.def)             Requested size: 173.33826pt x 103.99814pt.
<figs/P4_GCredit.pdf, id=1241, 722.7pt x 433.62pt>
File: figs/P4_GCredit.pdf Graphic file (type pdf)
<use figs/P4_GCredit.pdf>
Package pdftex.def Info: figs/P4_GCredit.pdf  used on input line 497.
(pdftex.def)             Requested size: 173.33826pt x 103.99814pt.
 [36

 </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/P4_COMPAS.pdf> </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/P4_GCredit.pdf>]
<figs/P5_COMPAS.pdf, id=1348, 722.7pt x 433.62pt>
File: figs/P5_COMPAS.pdf Graphic file (type pdf)
<use figs/P5_COMPAS.pdf>
Package pdftex.def Info: figs/P5_COMPAS.pdf  used on input line 525.
(pdftex.def)             Requested size: 151.66974pt x 90.99672pt.
pdfTeX warning (ext4): destination with the same identifier (name{fig:sfig1}) has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.525 \end{frame}
                 pdfTeX warning (ext4): destination with the same identifier (name{sub@fig:sfig1}) has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.525 \end{frame}
                 
<figs/P5_GCredit.pdf, id=1349, 722.7pt x 433.62pt>
File: figs/P5_GCredit.pdf Graphic file (type pdf)
<use figs/P5_GCredit.pdf>
Package pdftex.def Info: figs/P5_GCredit.pdf  used on input line 525.
(pdftex.def)             Requested size: 151.66974pt x 90.99672pt.
pdfTeX warning (ext4): destination with the same identifier (name{fig:sfig2}) has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.525 \end{frame}
                 pdfTeX warning (ext4): destination with the same identifier (name{sub@fig:sfig2}) has been already used, duplicate ignored
<to be read again> 
                   \relax 
l.525 \end{frame}
                 
<figs/P5_Adult.pdf, id=1350, 722.7pt x 433.62pt>
File: figs/P5_Adult.pdf Graphic file (type pdf)
<use figs/P5_Adult.pdf>
Package pdftex.def Info: figs/P5_Adult.pdf  used on input line 525.
(pdftex.def)             Requested size: 151.66974pt x 90.99672pt.
<figs/P5_Moons.pdf, id=1351, 722.7pt x 433.62pt>
File: figs/P5_Moons.pdf Graphic file (type pdf)
<use figs/P5_Moons.pdf>
Package pdftex.def Info: figs/P5_Moons.pdf  used on input line 525.
(pdftex.def)             Requested size: 151.66974pt x 90.99672pt.
 [37

 </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/P5_COMPAS.pdf> </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/P5_GCredit.pdf> </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/P5_Adult.pdf> </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/P5_Moons.pdf>] (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.toc) [38

] [39

] [40

] [41

] [42

] [43

] [44

]
<figs/gambsResults.png, id=1762, 749.80125pt x 205.76875pt>
File: figs/gambsResults.png Graphic file (type png)
<use figs/gambsResults.png>
Package pdftex.def Info: figs/gambsResults.png  used on input line 566.
(pdftex.def)             Requested size: 390.00697pt x 107.03175pt.
 [45

 </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/gambsResults.png>]
<figs/TramerResults.png, id=1791, 1202.4925pt x 301.125pt>
File: figs/TramerResults.png Graphic file (type png)
<use figs/TramerResults.png>
Package pdftex.def Info: figs/TramerResults.png  used on input line 578.
(pdftex.def)             Requested size: 390.00697pt x 97.66228pt.
 [46

 </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/TramerResults.png>]
<figs/CarliniResults.png, id=1820, 805.0075pt x 265.99374pt>
File: figs/CarliniResults.png Graphic file (type png)
<use figs/CarliniResults.png>
Package pdftex.def Info: figs/CarliniResults.png  used on input line 590.
(pdftex.def)             Requested size: 390.00697pt x 128.86475pt.
 [47

 </Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/figs/CarliniResults.png>] [48

]
\tf@nav=\write6
\openout6 = `main.nav'.

\tf@toc=\write7
\openout7 = `main.toc'.

\tf@snm=\write8
\openout8 = `main.snm'.

 (/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.aux)

LaTeX Warning: There were undefined references.


LaTeX Warning: There were multiply-defined labels.

Package rerunfilecheck Info: File `main.out' has not changed.
(rerunfilecheck)             Checksum: F2851B9FA14CE894F5AB2A96065D358B;1901.
 ) 
Here is how much of TeX's memory you used:
 27023 strings out of 478994
 522786 string characters out of 5858184
 811051 words of memory out of 5000000
 43762 multiletter control sequences out of 15000+600000
 421448 words of font info for 92 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 131i,15n,132p,544b,1331s stack positions out of 5000i,500n,10000p,200000b,80000s
pdfTeX warning (dest): name{fig:DTexample} has been referenced but does not exist, replaced by a fixed one

{/usr/local/texlive/2021/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc}</usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cmextra/cmex7.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi6.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi9.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmss10.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmss12.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmss17.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmss8.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmss9.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmssbx10.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmssi10.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmssi8.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmssi9.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy5.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy6.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy8.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy9.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/doublestroke/dsrom10.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/amsfonts/symbols/msbm10.pfb></usr/local/texlive/2021/texmf-dist/fonts/type1/public/cm-super/sfss0600.pfb>
Output written on "/Users/<USER>/Desktop/Polytechnique Mtl/Projet de recherche/ML_privacy/presentation_beamer/main.pdf" (48 pages, 1481517 bytes).
PDF statistics:
 1992 PDF objects out of 2073 (max. 8388607)
 1569 compressed objects within 16 object streams
 126 named destinations out of 1000 (max. 500000)
 683 words of extra memory for PDF output out of 10000 (max. 10000000)

