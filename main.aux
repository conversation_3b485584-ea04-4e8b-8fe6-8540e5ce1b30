\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyperFirstAtBeginDocument{\AtBeginDocument}
\HyperFirstAtBeginDocument{\ifx\hyper@anchor\@undefined
\global\let\oldcontentsline\contentsline
\gdef\contentsline#1#2#3#4{\oldcontentsline{#1}{#2}{#3}}
\global\let\oldnewlabel\newlabel
\gdef\newlabel#1#2{\newlabelxx{#1}#2}
\gdef\newlabelxx#1#2#3#4#5#6{\oldnewlabel{#1}{{#2}{#3}}}
\AtEndDocument{\ifx\hyper@anchor\@undefined
\let\contentsline\oldcontentsline
\let\newlabel\oldnewlabel
\fi}
\fi}
\global\let\hyper@last\relax 
\gdef\HyperFirstAtBeginDocument#1{#1}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\providecommand \oddpage@label [2]{}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{1}{1/1}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {1}{1}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{2}{2/2}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {2}{2}}}
\@writefile{toc}{\beamer@sectionintoc {1}{Background}{3}{0}{1}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {1}{2}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {1}{2}}}
\@writefile{nav}{\headcommand {\sectionentry {1}{Background}{3}{Background}{0}}}
\@writefile{nav}{\headcommand {\slideentry {1}{0}{1}{3/3}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {3}{3}}}
\@writefile{toc}{\beamer@subsectionintoc {1}{1}{Model extraction attacks}{4}{0}{1}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {3}{3}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{1}{1}{4}{Model extraction attacks}}}
\@writefile{nav}{\headcommand {\slideentry {1}{1}{1}{4/7}{Model extraction attacks}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {4}{7}}}
\@writefile{toc}{\beamer@subsectionintoc {1}{2}{Decision Trees}{8}{0}{1}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {4}{7}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{1}{2}{8}{Decision Trees}}}
\@writefile{nav}{\headcommand {\slideentry {1}{2}{1}{8/8}{Decision Trees}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {8}{8}}}
\@writefile{toc}{\beamer@subsectionintoc {1}{3}{Optimal counterfactual explanation}{9}{0}{1}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {8}{8}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{1}{3}{9}{Optimal counterfactual explanation}}}
\@writefile{nav}{\headcommand {\slideentry {1}{3}{1}{9/9}{Optimal counterfactual explanation}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {9}{9}}}
\providecommand*\caption@xref[2]{\@setref\relax\@undefined{#1}}
\newlabel{fig:CFexample}{{3}{10}{Example of the different types of counterfactual explanations (CF) quality (for $\mu =L_2$) for the previous tree (Figure~\ref {fig:DTexample}).\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:CFexample}{10}}
\@writefile{nav}{\headcommand {\slideentry {1}{3}{2}{10/10}{Optimal counterfactual explanation}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {10}{10}}}
\@writefile{nav}{\headcommand {\slideentry {1}{3}{3}{11/12}{Optimal counterfactual explanation}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {11}{12}}}
\@writefile{toc}{\beamer@sectionintoc {2}{Related work}{13}{0}{2}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {3}{12}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {9}{12}}}
\@writefile{nav}{\headcommand {\sectionentry {2}{Related work}{13}{Related work}{0}}}
\@writefile{nav}{\headcommand {\slideentry {2}{0}{1}{13/13}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {13}{13}}}
\@writefile{toc}{\beamer@subsectionintoc {2}{1}{Surrogate attacks}{14}{0}{2}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {13}{13}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{2}{1}{14}{Surrogate attacks}}}
\@writefile{nav}{\headcommand {\slideentry {2}{1}{1}{14/16}{Surrogate attacks}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {14}{16}}}
\@writefile{toc}{\beamer@subsectionintoc {2}{2}{Steal-ML}{17}{0}{2}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {14}{16}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{2}{2}{17}{Steal-ML}}}
\@writefile{nav}{\headcommand {\slideentry {2}{2}{1}{17/17}{Steal-ML}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {17}{17}}}
\@writefile{toc}{\beamer@sectionintoc {3}{Contribution}{18}{0}{3}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {13}{17}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {17}{17}}}
\@writefile{nav}{\headcommand {\sectionentry {3}{Contribution}{18}{Contribution}{0}}}
\@writefile{nav}{\headcommand {\slideentry {3}{0}{1}{18/18}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {18}{18}}}
\@writefile{toc}{\beamer@subsectionintoc {3}{1}{Tree Reconstruction Attack (TRA)}{19}{0}{3}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {18}{18}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{3}{1}{19}{Tree Reconstruction Attack (TRA)}}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces TRA algorithm\relax }}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces TRA algorithm\relax }}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces TRA algorithm\relax }}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces TRA algorithm\relax }}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces TRA algorithm\relax }}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces TRA algorithm\relax }}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces TRA algorithm\relax }}}
\@writefile{nav}{\headcommand {\slideentry {3}{1}{1}{19/25}{Tree Reconstruction Attack (TRA)}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {19}{25}}}
\@writefile{nav}{\headcommand {\slideentry {3}{1}{2}{26/27}{Tree Reconstruction Attack (TRA)}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {26}{27}}}
\@writefile{toc}{\beamer@sectionintoc {4}{Results}{28}{0}{4}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {18}{27}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {19}{27}}}
\@writefile{nav}{\headcommand {\sectionentry {4}{Results}{28}{Results}{0}}}
\@writefile{nav}{\headcommand {\slideentry {4}{0}{1}{28/28}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {28}{28}}}
\@writefile{toc}{\beamer@subsectionintoc {4}{1}{Theorical results}{29}{0}{4}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {28}{28}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{4}{1}{29}{Theorical results}}}
\@writefile{nav}{\headcommand {\slideentry {4}{1}{1}{29/30}{Theorical results}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {29}{30}}}
\@writefile{nav}{\headcommand {\slideentry {4}{1}{2}{31/33}{Theorical results}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {31}{33}}}
\@writefile{toc}{\beamer@subsectionintoc {4}{2}{Experimental results}{34}{0}{4}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {29}{33}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{4}{2}{34}{Experimental results}}}
\@writefile{nav}{\headcommand {\slideentry {4}{2}{1}{34/34}{Experimental results}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {34}{34}}}
\newlabel{tab:tab_2_results}{{\caption@xref {tab:tab_2_results}{ on input line 477}}{35}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {tab:tab_2_results}{35}}
\@writefile{nav}{\headcommand {\slideentry {4}{2}{2}{35/35}{Experimental results}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {35}{35}}}
\newlabel{fig:sfig1}{{7a}{36}{COMPAS dataset.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:sfig1}{36}}
\newlabel{sub@fig:sfig1}{{a}{36}{COMPAS dataset.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {sub@fig:sfig1}{36}}
\newlabel{fig:sfig2}{{7b}{36}{German Credit dataset.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:sfig2}{36}}
\newlabel{sub@fig:sfig2}{{b}{36}{German Credit dataset.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {sub@fig:sfig2}{36}}
\newlabel{fig:figTRAvsPathFinding}{{7}{36}{Plots of number of queries vs number of nodes for the COMPAS and German Credit datasets.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:figTRAvsPathFinding}{36}}
\@writefile{nav}{\headcommand {\slideentry {4}{2}{3}{36/36}{Experimental results}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {36}{36}}}
\newlabel{fig:sfig1}{{\caption@xref {fig:sfig1}{ on input line 525}}{37}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:sfig1}{37}}
\newlabel{sub@fig:sfig1}{{}{37}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {sub@fig:sfig1}{37}}
\newlabel{fig:sfig2}{{\caption@xref {fig:sfig2}{ on input line 525}}{37}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:sfig2}{37}}
\newlabel{sub@fig:sfig2}{{}{37}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {sub@fig:sfig2}{37}}
\newlabel{fig:sfig3}{{\caption@xref {fig:sfig3}{ on input line 525}}{37}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:sfig3}{37}}
\newlabel{sub@fig:sfig3}{{}{37}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {sub@fig:sfig3}{37}}
\newlabel{fig:sfig4}{{\caption@xref {fig:sfig4}{ on input line 525}}{37}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:sfig4}{37}}
\newlabel{sub@fig:sfig4}{{}{37}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {sub@fig:sfig4}{37}}
\newlabel{fig:figTRAvsCFandDualCF}{{8}{37}{Plots of means fidelity vs number of nodes.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:figTRAvsCFandDualCF}{37}}
\@writefile{nav}{\headcommand {\slideentry {4}{2}{4}{37/37}{Experimental results}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {37}{37}}}
\@writefile{toc}{\beamer@sectionintoc {5}{Conclusion and Future works}{38}{0}{5}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {28}{37}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {34}{37}}}
\@writefile{nav}{\headcommand {\sectionentry {5}{Conclusion and Future works}{38}{Conclusion and Future works}{0}}}
\@writefile{nav}{\headcommand {\slideentry {5}{0}{1}{38/38}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {38}{38}}}
\@writefile{nav}{\headcommand {\slideentry {5}{0}{2}{39/43}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {39}{43}}}
\@writefile{nav}{\headcommand {\slideentry {5}{0}{3}{44/44}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {44}{44}}}
\newlabel{fig:Gambs-label}{{9}{45}{Model extraction from counterfactual explanations, Aïvodji et al. 2020\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:Gambs-label}{45}}
\@writefile{nav}{\headcommand {\slideentry {5}{0}{4}{45/45}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {45}{45}}}
\newlabel{fig:Tramer-label}{{10}{46}{Stealing Machine Learning Models via Prediction APIs, Tramer et al. 2016\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:Tramer-label}{46}}
\@writefile{nav}{\headcommand {\slideentry {5}{0}{5}{46/46}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {46}{46}}}
\newlabel{fig:Carlini-label}{{11}{47}{Stealing Part of a Production Language Model, Carlini et al. 2024\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:Carlini-label}{47}}
\@writefile{nav}{\headcommand {\slideentry {5}{0}{6}{47/47}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {47}{47}}}
\@writefile{nav}{\headcommand {\slideentry {5}{0}{7}{48/48}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {48}{48}}}
\@writefile{nav}{\headcommand {\beamer@partpages {1}{48}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {38}{48}}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {38}{48}}}
\@writefile{nav}{\headcommand {\beamer@documentpages {48}}}
\@writefile{nav}{\headcommand {\gdef \inserttotalframenumber {28}}}
\gdef \@abspage@last{48}
