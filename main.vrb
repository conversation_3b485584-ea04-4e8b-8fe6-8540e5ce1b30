\frametitle{Tree Reconstruction Attack}
  \hspace{0.3cm}
  \begin{minipage}{0.55\textwidth}
        \scriptsize
        \setcounter{algocf}{0}
        \begin{algorithm}[H]
        \SetAlgoLined
        \uncover<1->{\alert<1>{\textbf{Require} A model \(f\), a counterfactual explanation oracle \(\mathcal{O}_\mu\), a node \(node\) (root node in the beginning), and a bounded input space \(\mathcal{E} \subset \mathbb{R}^d\)\\}}

        \vspace{0.3cm}
        \uncover<2->{\alert<2>{\(x \gets \text{center}(\mathcal{E})\)\\}}
        \uncover<3->{\alert<3>{\(x', y \gets \mathcal{O}_\mu(f,x,\mathcal{E}), f(x)\)\\}}
        \uncover<7->{\alert<7>{\If{\(x' \text{does not exists}\)}{
            \(node \gets leaf(y)\)\\
            \textbf{return}\\
        }}}
        \uncover<4->{\alert<4>{\(i, v \gets \{(i, x'_i) , x_i \neq x'_i\} \)\\}}
        \uncover<4->{\alert<4>{\(node \gets node(i, v)\)\\}}
        \uncover<4->{\alert<4>{\(\mathcal{E}_1, \mathcal{E}_2 \gets \text{split}(\mathcal{E},node)\)\\}}
        \uncover<5->{\alert<5>{\textbf{TRA} (\(f, \mathcal{O}_\mu, node.left, \mathcal{E}_1\))\\}}
        \uncover<5->{\alert<5>{\textbf{TRA} (\(f, \mathcal{O}_\mu, node.right, \mathcal{E}_2\))\\}}

         \caption{TRA algorithm}
        \end{algorithm}

    \end{minipage}
    \begin{minipage}{0.4\textwidth}
         \centering
         % Graph

        \begin{tikzpicture}[scale=2.9]
            % Dessiner les axes
            \draw[->] (0,0) -- (1.05,0) node[right] {$x_1$};
            \draw[->] (0,0) -- (0,1.05) node[above] {$x_2$};

            % Ajouter les lignes
            \draw[thick] (-0.02,0.7) -- (1,0.7) node[left, xshift=-7.5em] {\tiny $0.7$};
            \draw[thick] (0.6,-0.02) -- (0.6,1) node[below, yshift=-7.5em] {\tiny $0.6$};
            \node at (-0.05,-0.05) {\tiny $0$};

            % Colorer les zones
            \fill[red!70] (0,0.7) rectangle (0.6,1);
            \fill[red!70] (0.6,0) rectangle (1,0.7);
            \fill[blue!70] (0,0) rectangle (0.6,0.7);
            \fill[blue!70] (0.6,0.7) rectangle (1,1);

            \draw[thick] (-0.02,1.0) -- (1.0,1.0) node[left, xshift=-7.5em] {\tiny $1$};
            \draw[thick] (1.0,-0.02) -- (1.0,1.0) node[below, yshift=-7.5em] {\tiny $1$};
        \end{tikzpicture}
        \\
        \begin{tikzpicture}[scale=2.9]
            % Dessiner les axes
            \draw[->] (0,0) -- (1.05,0) node[right] {$x_1$};
            \draw[->] (0,0) -- (0,1.05) node[above] {$x_2$};

            % Ajouter les lignes
            \only<6->{\draw[thick] (-0.02,0.7) -- (1,0.7) node[left, xshift=-7.5em] {\tiny $0.7$};}
            \only<4->{\draw[thick] (0.6,-0.02) -- (0.6,1) node[below, yshift=-7.5em] {\tiny $0.6$};}
            \node at (-0.05,-0.05) {\tiny $0$};

            % Colorer les zones
            \only<7->\fill[red!70] (0,0.7) rectangle (0.6,1);
            \only<7->\fill[red!70] (0.6,0) rectangle (1,0.7);
            \only<7->\fill[blue!70] (0,0) rectangle (0.6,0.7);
            \only<7->\fill[blue!70] (0.6,0.7) rectangle (1,1);

            \draw[thick] (-0.02,1.0) -- (1.0,1.0) node[left, xshift=-7.5em] {\tiny $1$};
            \draw[thick] (1.0,-0.02) -- (1.0,1.0) node[below, yshift=-7.5em] {\tiny $1$};

            % 1st query
            \only<2-4>{\node at (0.5, 0.5) {\tiny $x$};}
            \only<3-4>{\node at (0.6, 0.5) {\tiny \textcolor{green}{$x$}};}

            % 2nd and 3rd queries
            \only<5>{\node at (0.3, 0.5) {\tiny $x$};}
            \only<5>{\node at (0.3, 0.7) {\tiny \textcolor{green}{$x$}};}
            \only<5>{\node at (0.8, 0.5) {\tiny $x$};}
            \only<5>{\node at (0.8, 0.7) {\tiny \textcolor{green}{$x$}};}

            %leaf node queries
            \only<6>{\node at (0.3, 0.35) {\tiny $x$};}
            \only<6>{\node at (0.3, 0.85) {\tiny $x$};}
            \only<6>{\node at (0.8, 0.35) {\tiny $x$};}
            \only<6>{\node at (0.8, 0.85) {\tiny $x$};}

        \end{tikzpicture}

    \end{minipage}
